"""
Message filtering utilities to prevent noisy debug lines and non-SMS content
from being logged or saved to the database.
"""

import logging
import time
from typing import List, Optional, Dict, Any
from .config_manager import load_config

logger = logging.getLogger(__name__)

# Cache for configuration to avoid repeated loading
_config_cache = None
_config_cache_time = 0
_config_cache_ttl = 60  # Cache for 60 seconds

def _get_cached_config() -> Dict[str, Any]:
    """Get configuration with caching to avoid repeated file loads."""
    global _config_cache, _config_cache_time

    current_time = time.time()

    # Check if cache is valid
    if _config_cache is None or (current_time - _config_cache_time) > _config_cache_ttl:
        try:
            _config_cache = load_config()
            _config_cache_time = current_time
        except Exception as e:
            logger.error(f"Error loading config for filtering: {e}")
            # Return empty config as fallback
            _config_cache = {}

    return _config_cache

def clear_config_cache():
    """Clear the configuration cache to force reload on next access."""
    global _config_cache, _config_cache_time
    _config_cache = None
    _config_cache_time = 0

def should_ignore_log_message(message: str) -> bool:
    """
    Check if a log message should be ignored based on configured patterns.

    Args:
        message: The log message to check

    Returns:
        True if the message should be ignored, False otherwise
    """
    try:
        config = _get_cached_config()
        ignore_patterns = config.get('ignore_log_patterns', [])

        if not ignore_patterns:
            return False

        message_lower = message.lower()

        for pattern in ignore_patterns:
            if pattern.lower() in message_lower:
                return True

        return False

    except Exception as e:
        logger.error(f"Error checking log ignore patterns: {e}")
        return False


def should_ignore_modem_response(line: str) -> bool:
    """
    Check if a modem response line should be ignored and not saved as a message.

    Args:
        line: The modem response line to check

    Returns:
        True if the line should be ignored, False otherwise
    """
    try:
        config = _get_cached_config()
        ignore_patterns = config.get('ignore_message_patterns', [])
        
        # Default patterns if not configured
        if not ignore_patterns:
            ignore_patterns = [
                "OK", "ERROR", "", "AT+", "^", "+CREG:", "+CSQ:", 
                "+COPS:", "+CPIN:", "RING", "NO CARRIER", "BUSY", 
                "NO ANSWER", "CONNECT"
            ]
        
        if not line or not line.strip():
            return True
            
        line_stripped = line.strip()
        line_upper = line_stripped.upper()
        
        # Check against configured patterns
        for pattern in ignore_patterns:
            pattern_upper = pattern.upper()

            # Skip empty patterns
            if not pattern_upper:
                if not line_stripped:  # Empty line matches empty pattern
                    return True
                continue

            # Exact match for short patterns (unless they're special prefixes)
            if len(pattern_upper) <= 3 and pattern_upper not in ["AT+", "^"]:
                if line_upper == pattern_upper:
                    return True

            # Prefix match for patterns ending with ':' or starting with special chars
            elif (pattern_upper.endswith(':') or
                  pattern_upper.startswith('AT+') or
                  pattern_upper.startswith('^')):
                if line_upper.startswith(pattern_upper):
                    return True

            # Exact match for longer patterns
            elif len(pattern_upper) > 3:
                if line_upper == pattern_upper:
                    return True
        
        # Additional checks for common modem responses
        if (line_upper.startswith("AT") or 
            line_upper.startswith("^") or
            line_upper in ["OK", "ERROR"] or
            len(line_stripped) == 0):
            return True
            
        return False
        
    except Exception as e:
        logger.error(f"Error checking message ignore patterns: {e}")
        return False


def is_valid_sms_content(line: str) -> bool:
    """
    Check if a line contains valid SMS content that should be processed.

    Args:
        line: The line to check

    Returns:
        True if the line contains valid SMS content, False otherwise
    """
    # First check if it should be ignored as a modem response
    if should_ignore_modem_response(line):
        return False

    # Additional validation for SMS content
    line_stripped = line.strip()

    # Must have some content
    if not line_stripped:
        return False

    # Should not be just numbers (unless it's a reasonable length for a verification code)
    if line_stripped.isdigit() and len(line_stripped) < 4:
        return False

    # Should not be just punctuation
    if all(c in '.,;:!?-_()[]{}' for c in line_stripped):
        return False

    # If it contains letters or reasonable length numbers, it's likely valid SMS content
    has_letters = any(c.isalpha() for c in line_stripped)
    has_reasonable_numbers = line_stripped.isdigit() and 4 <= len(line_stripped) <= 10
    has_mixed_content = any(c.isalpha() for c in line_stripped) and any(c.isdigit() for c in line_stripped)

    return has_letters or has_reasonable_numbers or has_mixed_content


def filter_log_message(message: str) -> Optional[str]:
    """
    Filter a log message, returning None if it should be ignored.
    
    Args:
        message: The log message to filter
        
    Returns:
        The message if it should be logged, None if it should be ignored
    """
    if should_ignore_log_message(message):
        return None
    return message


def get_filtered_logger(name: str):
    """
    Get a logger that automatically filters messages based on configuration.
    
    Args:
        name: Logger name
        
    Returns:
        A logger instance with filtering
    """
    base_logger = logging.getLogger(name)
    
    class FilteredLogger:
        def __init__(self, logger):
            self._logger = logger
            
        def debug(self, msg, *args, **kwargs):
            if not should_ignore_log_message(str(msg)):
                self._logger.debug(msg, *args, **kwargs)
                
        def info(self, msg, *args, **kwargs):
            if not should_ignore_log_message(str(msg)):
                self._logger.info(msg, *args, **kwargs)
                
        def warning(self, msg, *args, **kwargs):
            if not should_ignore_log_message(str(msg)):
                self._logger.warning(msg, *args, **kwargs)
                
        def error(self, msg, *args, **kwargs):
            # Always log errors
            self._logger.error(msg, *args, **kwargs)
            
        def critical(self, msg, *args, **kwargs):
            # Always log critical messages
            self._logger.critical(msg, *args, **kwargs)
            
        def __getattr__(self, name):
            # Delegate other attributes to the base logger
            return getattr(self._logger, name)
    
    return FilteredLogger(base_logger)
