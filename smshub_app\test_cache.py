#!/usr/bin/env python3
"""
Simple test to verify config caching is working.
"""

import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

print("Testing config caching...")

try:
    from app.message_filter import should_ignore_log_message, should_ignore_modem_response
    
    print("Testing multiple calls to see if config is cached:")
    
    # These should only load config once
    print("Call 1:", should_ignore_log_message("readline() timed out"))
    print("Call 2:", should_ignore_log_message("Top of main loop"))  
    print("Call 3:", should_ignore_modem_response("OK"))
    print("Call 4:", should_ignore_modem_response("ERROR"))
    print("Call 5:", should_ignore_log_message("some other message"))
    
    print("If caching works, you should only see ONE 'Successfully loaded configuration' message above.")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
